<template>
	<view @touchmove.stop.prevent>
		<view class="tui-popup-class tui-bottom-popup" :class="{ 'tui-popup-show': show, 'tui-popup-radius': radius }" :style="{ background: backgroundColor, height: height ? height + 'rpx' : 'auto', zIndex: zIndex,transform:`translate3d(0, ${show?translateY:'100%'}, 0)`}">
			<slot></slot>
			<!-- 添加右上角关闭icon功能 -->
			<view v-if="showCloseIcon" class="tui-close-icon" @tap="handleClose">
				<text class="iconfont icon-ic_close f-s-24"></text>
			</view>
		</view>
		<view class="tui-popup-mask" :class="[show ? 'tui-mask-show' : '']" :style="{ zIndex: maskZIndex }" v-if="mask" @tap="handleClose"></view>
	</view>
</template>

<script>
	export default {
		name: 'tuiBottomPopup',
		emits: ['close'],
		props: {
			//是否需要mask
			mask: {
				type: Boolean,
				default: true
			},
			//控制显示
			show: {
				type: Boolean,
				default: false
			},
			//背景颜色
			backgroundColor: {
				type: String,
				default: '#fff'
			},
			//高度 rpx
			height: {
				type: Number,
				default: 0
			},
			//设置圆角
			radius: {
				type: Boolean,
				default: true
			},
			zIndex: {
				type: [Number, String],
				default: 997
			},
			maskZIndex: {
				type: [Number, String],
				default: 996
			},
			//弹层显示时，垂直方向移动的距离
			translateY: {
				type: String,
				default: '0'
			},
			//是否显示右上角关闭图标
			showCloseIcon: {
				type: Boolean,
				default: true
			}
		},
		methods: {
			handleClose() {
				if (!this.show) {
					return;
				}
				this.$emit('close', {});
			}
		}
	};
</script>

<style scoped lang="scss">
	.tui-bottom-popup {
		width: 750rpx;
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		opacity: 0;
		transform: translate3d(0, 100%, 0);
		transform-origin: center;
		transition: all 0.3s ease-in-out;
		min-height: 20rpx;
		padding-bottom: env(safe-area-inset-bottom);
	}

	.tui-popup-radius {
		border-top-left-radius: 40rpx;
		border-top-right-radius: 40rpx;
		overflow: hidden;
	}
	

	.tui-popup-show {
		opacity: 1;
		/* transform: translate3d(0, 0, 0); */
	}

	.tui-popup-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.6);
		transition: all 0.3s ease-in-out;
		opacity: 0;
		visibility: hidden;
	}

	.tui-mask-show {
		opacity: 1;
		visibility: visible;
	}

	.tui-close-icon {
		position: absolute;
		top: 20rpx;
		right: 30rpx;
		// width: 40rpx;
		// height: 40rpx;
		// border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10;
	}

	.tui-close-text {
		color: #000;
		font-size: 40rpx;
		font-weight: bold;
		line-height: 1;
	}

</style>
