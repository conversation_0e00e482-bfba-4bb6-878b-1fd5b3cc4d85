* {
	box-sizing: border-box;
}

.shop-name {
	color: #131313;
	font-size: 28rpx;
	font-weight: 500;
}

/* 标签 */
.page-order-tags {
	height: 60rpx;
	display: flex;
	align-items: center;
	gap: 16rpx;

	.page-order-tags-item {
		width: fit-content;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 8rpx 16rpx;
		background-color: #EBEDF0;
		border-radius: 8px;
		gap: 8rpx;

		.page-order-tags-item-icon {
			width: 32rpx;
			height: 32rpx;

			.page-order-tags-item-icon-img {
				width: 100%;
				height: 100%;
			}
		}

		.page-order-tags-item-text {
			color: #666;
			font-size: 24rpx;
			font-weight: 400;
		}
	}
}

// 管理
.manage {
	color: #666;
	font-size: 28rpx;
	font-weight: 400;
}

/* 商品图片 */
.picture-wrapper {
	width: 180rpx;
	height: 180rpx;
	flex-shrink: 0;
	image {
		border-radius: 16rpx;
		width: 100%;
		height: 100%;
	}
}

// 商品信息
.goods-info {
	flex: 1;
	margin-left: 14rpx;
	min-width: 0; // 防止flex子元素撑开容器
	overflow: hidden; // 隐藏超出部分

	/* 商品名称 */
	.goods-name {
		color: #131313;
		font-size: 28rpx;
		font-weight: 400;
		line-height: normal;
		width: 100%;
	}

	/* 商品标签 */
	.goods-tags {
		display: flex;
		gap: 24rpx;
		margin: 16rpx 0;

		.goods-tag {
			padding: 4rpx 8rpx;
			background-color: rgba(255, 232, 232, 0.597);
			border-radius: 8rpx;

			&-text {
				color: #ff0000;
				font-size: 20rpx;
				font-weight: 400;
			}
		}
	}

	// 商品价格
	.goods-price {
		display: flex;
		align-items: baseline;
		gap: 8rpx;
	}

	.price-label {
		color: #ff0000;
		font-size: 24rpx;
		font-weight: 400;
		font-family: 'PingFang SC', sans-serif;
	}

	.price-symbol {
		color: #ff0000;
		font-size: 28rpx;
		font-weight: 600;
		font-family: 'PingFang SC', sans-serif;
	}

	.price-value {
		color: #ff0000;
		font-size: 40rpx;
		font-weight: 600;
		font-family: 'PingFang SC', sans-serif;
	}
}