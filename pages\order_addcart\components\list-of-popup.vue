<template>
    <view class="appearance-list-wrapper">
        <text class="header-text">{{ title }}</text>
        <view class="appearance-list">
            <view
                class="appearance-list-item tag-item"
                v-for="(item, index) in list"
                :key="item.id || index"
                :class="{ active: activeIndex === index }"
                @click="handleItemClick(index)"
            >
                <text>{{ item.name }}</text>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'ListOfPopup',
    props: {
        // 列表数据
        list: {
            type: Array,
            default: () => []
        },
        // 标题文本
        title: {
            type: String,
            default: '选项'
        },
        // 默认选中的索引
        defaultActiveIndex: {
            type: Number,
            default: 0
        },
        // 是否允许取消选择
        allowDeselect: {
            type: Boolean,
            default: false
        }
    },
    emits: ['change', 'select'],
    data() {
        return {
            activeIndex: this.defaultActiveIndex
        }
    },
    watch: {
        defaultActiveIndex: {
            handler(newVal) {
                this.activeIndex = newVal;
            },
            immediate: true
        }
    },
    computed: {
        selectedItem() {
            return this.list[this.activeIndex] || null;
        }
    },
    methods: {
        handleItemClick(index) {
            // 如果允许取消选择且点击的是当前选中项，则取消选择
            if (this.allowDeselect && this.activeIndex === index) {
                this.activeIndex = -1;
            } else {
                this.activeIndex = index;
            }

            // 触发事件，传递选中的索引和数据
            this.$emit('change', {
                index: this.activeIndex,
                item: this.selectedItem
            });

            // 兼容性事件
            this.$emit('select', this.activeIndex, this.selectedItem);
        },

        // 外部调用方法：设置选中项
        setActiveIndex(index) {
            if (index >= 0 && index < this.list.length) {
                this.activeIndex = index;
            }
        },

        // 外部调用方法：获取当前选中项
        getSelectedItem() {
            return this.selectedItem;
        },

        // 外部调用方法：获取当前选中索引
        getActiveIndex() {
            return this.activeIndex;
        }
    }
}
</script>

<style lang="scss" scoped>
.appearance-list-wrapper {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
}

.header-text {
    color: #131313;
    font-size: 28rpx;
    font-weight: 500;
}

.appearance-list {
    display: flex;
    gap: 32rpx;
    flex-wrap: wrap; // 允许换行，适应更多选项

    .appearance-list-item {
        color: #131313;
        font-size: 28rpx;
        font-weight: 500;
    }
}

.tag-item {
    border-radius: 8rpx;
    border: 2rpx solid transparent;
    padding: 12rpx 24rpx;
    background: rgba(255, 219, 219, 0.2);
    color: #131313;
    font-size: 28rpx;
    font-weight: 400;
    transition: all 0.3s ease; // 添加过渡效果
    cursor: pointer; // 添加手势提示

    &.active {
        border-color: #F00;
        color: #F00;
        background: rgba(255, 0, 0, 0.1); // 选中时背景稍微加深
    }

    &:hover {
        opacity: 0.8; // 悬停效果
    }
}
</style>